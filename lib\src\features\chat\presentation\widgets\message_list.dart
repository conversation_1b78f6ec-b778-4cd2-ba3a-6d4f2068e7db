import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:just_audio/just_audio.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/models/message_model.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';
import 'package:xinglian/src/common/widgets/rich_typewriter_text.dart';


class MessageList extends StatefulWidget {
  final List<Message> messages;
  final List<Agent> participants;
  final bool isStoryMode;
  final EdgeInsets? customPadding;
  final Agent? protagonistAgent;
  final ScrollController? scrollController;

  const MessageList({
    super.key,
    required this.messages,
    required this.participants,
    this.isStoryMode = false,
    this.customPadding,
    this.protagonistAgent,
    this.scrollController,
  });

  @override
  State<MessageList> createState() => _MessageListState();
}

class _MessageListState extends State<MessageList> {
  late final ScrollController _scrollController;
  final List<Message> _internalMessages = [];

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _internalMessages.addAll(widget.messages);
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100) {
      final bloc = context.read<ChatPlayerBloc>();
      if (bloc.state.hasMoreMessages && !bloc.state.isReplying) {
        bloc.add(const LoadMoreMessages());
      }
    }
  }

  @override
  void didUpdateWidget(covariant MessageList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.messages != oldWidget.messages) {
      setState(() {
        _internalMessages.clear();
        _internalMessages.addAll(widget.messages);
      });
      if (widget.messages.length > oldWidget.messages.length) {
        WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
      }
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.minScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }
  
  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_internalMessages.isEmpty) {
      return const Center(child: Text('开始对话吧...', style: TextStyle(color: Colors.white54)));
    }

    return ListView.builder(
      key: ValueKey('message_list_${_internalMessages.length}'),
      controller: _scrollController,
      padding: widget.customPadding ?? const EdgeInsets.fromLTRB(16, 100, 16, 200),
      itemCount: _internalMessages.length,
      reverse: true,
      itemBuilder: (context, index) {
        final reversedIndex = _internalMessages.length - 1 - index;
        final message = _internalMessages[reversedIndex];
        final agent = widget.participants.where((p) => p.id == message.agentId).isNotEmpty
            ? widget.participants.firstWhere((p) => p.id == message.agentId)
            : null;

        return MessageBubble(
          key: ValueKey('bubble_${message.id}'),
          message: message,
          agent: agent,
          isStoryMode: widget.isStoryMode,
          isHistoryMessage: message.isHistoryMessage,
          protagonistAgent: widget.protagonistAgent,
        );
      },
    );
  }
}

class MessageBubble extends StatefulWidget {
  final Message message;
  final Agent? agent;
  final bool isStoryMode;
  final bool isHistoryMessage;
  final Agent? protagonistAgent;

  const MessageBubble({
    super.key,
    required this.message,
    this.agent,
    required this.isStoryMode,
    this.isHistoryMessage = false,
    this.protagonistAgent,
  });

  @override
  State<MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<MessageBubble> with SingleTickerProviderStateMixin {
  static final _audioPlayer = AudioPlayer();
  static String? _lastAutoPlayedUrl;
  
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  StreamSubscription? _playerStateSubscription;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );

    _playerStateSubscription = _audioPlayer.playerStateStream.listen((state) {
      if (!mounted) return;
      final isCurrent = (_audioPlayer.audioSource as UriAudioSource?)?.tag == widget.message.audioUrl;
      if (state.playing && isCurrent) {
        // (audio logic unchanged)
      } else {
        // (audio logic unchanged)
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!widget.isHistoryMessage) {
        _animationController.forward();
      }

      if (widget.message.audioUrl != null &&
          widget.message.audioUrl!.isNotEmpty &&
          widget.message.role == MessageRole.assistant &&
          widget.message.audioUrl != _lastAutoPlayedUrl) {
        _playAudio(widget.message.audioUrl!);
        _lastAutoPlayedUrl = widget.message.audioUrl;
      }
    });
  }

  @override
  void dispose() {
    _playerStateSubscription?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _playAudio(String url) async {
    try {
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }
      final source = AudioSource.uri(Uri.parse(url), tag: url);
      await _audioPlayer.setAudioSource(source);
      _audioPlayer.play();
    } catch (e) {
      print("播放音频失败: $e");
    }
  }

  Widget _buildFormattedText(String text) {
    // (this function remains unchanged)
    final List<InlineSpan> children = [];
    String processedText = text.replaceAll(RegExp(r'["""'']'), '');
    final RegExp regExp = RegExp(r'([（(][^）)]*[）)])');

    processedText.splitMapJoin(
      regExp,
      onMatch: (Match match) {
        children.add(TextSpan(
          text: match.group(0),
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontStyle: FontStyle.italic,
            fontSize: 16,
            height: 1.5,
          ),
        ));
        return '';
      },
      onNonMatch: (String nonMatch) {
        if (nonMatch.trim().isNotEmpty) {
          children.add(TextSpan(
            text: nonMatch,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              height: 1.5,
            ),
          ));
        }
        return '';
      },
    );

    return Text.rich(
      TextSpan(children: children),
      key: ValueKey('text_${widget.message.id}'),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 修复：当主角Agent存在时，所有role为user的消息都应被视作主角消息
    final isProtagonistMessage = widget.isStoryMode &&
                               widget.protagonistAgent != null &&
                               widget.message.role == MessageRole.user;

    final isUser = widget.message.role == MessageRole.user;
    final isNarration = widget.message.role == MessageRole.narration;

    if (isNarration) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
        child: Text(
          widget.message.content,
          textAlign: TextAlign.left,
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14, fontStyle: FontStyle.italic, height: 1.5),
        ),
      );
    }

    String displayName;
    String? displayAvatar;

    // --- ▼▼▼ 核心修复：正确设置主角的显示名称和头像 ▼▼▼ ---
    if (isProtagonistMessage) {
        displayName = widget.protagonistAgent!.name;
        displayAvatar = widget.protagonistAgent!.avatarUrl;
    } else if (isUser) {
        displayName = "我";
        displayAvatar = null;
    } else {
        displayName = widget.agent?.name ?? '角色';
        displayAvatar = widget.agent?.avatarUrl;
    }
    // --- ▲▲▲ 修复结束 ▲▲▲ ---

    final alignment = isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start;

    Widget? avatarWidget;
    // 只有非用户（NPC），才在这里构建头像
    if (!isUser) {
      if (displayAvatar != null && displayAvatar.isNotEmpty) {
        avatarWidget = ClipOval(
          child: SizedBox.fromSize(
            size: const Size.fromRadius(20),
            child: Image.network(
              displayAvatar,
              fit: BoxFit.cover,
              alignment: Alignment.topCenter,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: AppColors.inputBackground,
                  child: const Icon(Icons.person, color: AppColors.secondaryText, size: 20),
                );
              },
            ),
          ),
        );
      } else {
        avatarWidget = const CircleAvatar(
          radius: 20,
          child: Icon(Icons.support_agent),
        );
      }
    }

    // --- 变更点 3：“我”的头像占位符 ---
    final Widget userAvatarWidget = CircleAvatar(
      radius: 20,
      backgroundColor: AppColors.accentPurple.withOpacity(0.5),
      child: Text(
        displayName.isNotEmpty ? displayName[0] : "我",
        style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)
      ),
    );

    final bubbleWidget = Flexible(
      child: Column(
        crossAxisAlignment: alignment,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              displayName,
              style: TextStyle(color: Colors.white.withOpacity(0.8), fontSize: 12),
            ),
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  // --- 变更点 5：“我”的聊天气泡背景 ---
                  gradient: isUser ? LinearGradient(
                    colors: [
                      AppColors.accentPurple.withOpacity(0.35),
                      AppColors.accentBlue.withOpacity(0.25),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ) : null,
                  color: isUser
                      ? null // 用户消息：使用渐变
                      : Colors.black.withOpacity(0.3), // AI消息：半透明黑色
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.white.withOpacity(0.2)),
                ),
                child: isUser
                  // 核心修改：用户消息直接显示，无动画
                  ? _buildFormattedText(widget.message.content)
                  // NPC消息：历史消息直接显示，新消息使用修改后的打字机组件（已移除固定延迟）
                  : widget.isHistoryMessage
                    ? _buildFormattedText(widget.message.content)
                    : RichTypewriterText(
                        widget.message.content,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          height: 1.5,
                        ),
                        skipAnimation: widget.isHistoryMessage,
                      ),
              ),
            ),
          ),
        ],
      ),
    );

    final List<Widget> children = [];
    if (isUser) { // 用户（主角）消息在右边
      // --- 变更点 4：聊天气泡略微左移 ---
      children.add(const SizedBox(width: 48)); // 左侧留出与NPC对称的间距
      children.add(bubbleWidget);
      children.add(const SizedBox(width: 8));
      children.add(userAvatarWidget); // 我的头像
    } else { // AI消息在左边
      if (avatarWidget != null) {
        children.add(avatarWidget);
        children.add(const SizedBox(width: 8));
      }
      children.add(bubbleWidget);
      // --- 变更点 4：聊天气泡略微左移 ---
      children.add(const SizedBox(width: 48)); // 右侧留出与用户对称的间距
    }

    // 核心修改：用户消息和历史消息都不显示动画
    if (widget.isHistoryMessage || isUser) {
      return _buildBubbleContent(alignment, children);
    } else {
      // 只有NPC的新消息才显示气泡动画
      return FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: _buildBubbleContent(alignment, children),
        ),
      );
    }
  }

  Widget _buildBubbleContent(CrossAxisAlignment alignment, List<Widget> children) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: alignment == CrossAxisAlignment.end ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }
}
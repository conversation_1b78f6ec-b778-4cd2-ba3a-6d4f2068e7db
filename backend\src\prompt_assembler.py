#!/usr/bin/env python3
"""
PromptAssembler - RAG记忆系统的核心组件
负责动态组装上下文，集成RAG检索结果、对话摘要和近期历史
"""

import asyncio
from typing import List, Dict, Any, Optional, Literal
from src.services.unified_llm_service import llm_service
from src.supabase_service import supabase_service
from src.summarization_service import summarization_service
from src.prompt_templates import ADVANCED_STORY_CHAT_PROMPT


class PromptAssembler:
    """
    动态提示词组装器
    将角色设定、RAG记忆、对话摘要和近期历史智能组合
    """
    
    def __init__(self):
        self.max_context_tokens = 8000  # 预留给上下文的最大token数
        self.recent_messages_limit = 10  # 近期消息数量限制
        self.rag_memories_limit = 5     # RAG检索记忆数量限制

    async def build_fast_prompt(
        self,
        chat_id: str,
        user_message: str,
        agent_id: str,
        mode: Literal['chat', 'story'] = 'chat'
    ) -> str:
        """
        构建快速提示词，不包含RAG搜索，用于立即响应

        Args:
            chat_id: 会话ID
            user_message: 用户当前输入
            agent_id: AI角色ID
            mode: 对话模式，'chat'为角色聊天，'story'为故事模式

        Returns:
            快速构建的提示词字符串
        """
        try:
            # 1. 获取模式感知的角色配置
            agent_info = await self._get_agent_with_mode_config(agent_id, mode)
            if not agent_info:
                raise Exception(f"未找到角色 {agent_id}")

            # 2. 并行获取基础数据（不包含需要embedding的RAG搜索）
            tasks = {
                "history": supabase_service.get_messages_by_chat_id(
                    chat_id=chat_id,
                    limit=self.recent_messages_limit
                ),
                "world_info": self._get_world_info_entries(agent_id, user_message)
            }

            # 3. 根据模式决定是否获取摘要或故事任务
            if mode == 'chat':
                tasks["summary"] = summarization_service.get_chat_summary(chat_id)
            elif mode == 'story':
                tasks["story_chapter"] = self._get_story_chapter_info(chat_id)

            # 执行所有查询
            results = await asyncio.gather(*[tasks[key] for key in tasks.keys()])
            # 将结果重新映射回字典
            results_map = dict(zip(tasks.keys(), results))

            # 添加空的RAG结果
            results_map["rag"] = []

            # 4. 组装最终提示词
            prompt = await self._assemble_final_prompt_v3(
                agent_info=agent_info,
                results_map=results_map,
                user_message=user_message,
                mode=mode,
                chat_id=chat_id
            )

            print(f"SUCCESS: 为会话 {chat_id} 构建了{mode}模式的快速提示词（无RAG）")
            return prompt

        except Exception as e:
            print(f"ERROR: 构建快速提示词失败: {e}")
            # 降级方案：返回基础提示词
            return await self._build_fallback_prompt(agent_id, user_message)
        
    async def build_prompt(
        self,
        chat_id: str,
        user_message: str,
        agent_id: str,
        mode: Literal['chat', 'story'] = 'chat'
    ) -> str:
        """
        V3版: 根据模式 (mode) 动态构建上下文

        Args:
            chat_id: 会话ID
            user_message: 用户当前输入
            agent_id: AI角色ID
            mode: 对话模式，'chat'为角色聊天，'story'为故事模式

        Returns:
            完整的提示词字符串
        """
        try:
            # 1. 获取模式感知的角色配置
            agent_info = await self._get_agent_with_mode_config(agent_id, mode)
            if not agent_info:
                raise Exception(f"未找到角色 {agent_id}")

            # 2. 生成用户消息的向量表示
            user_embedding = await llm_service.get_embedding(user_message)

            # 3. 并行获取通用数据
            tasks = {
                "rag": supabase_service.search_chat_memories(
                    chat_id=chat_id,
                    query_embedding=user_embedding,
                    match_threshold=0.75,
                    match_count=self.rag_memories_limit
                ),
                "history": supabase_service.get_messages_by_chat_id(
                    chat_id=chat_id,
                    limit=self.recent_messages_limit
                ),
                "world_info": self._get_world_info_entries(agent_id, user_message)
            }

            # 4. 根据模式决定是否获取摘要或故事任务
            if mode == 'chat':
                tasks["summary"] = summarization_service.get_chat_summary(chat_id)
            elif mode == 'story':
                # 在故事模式下，我们获取章节任务和游戏状态
                tasks["story_chapter"] = self._get_story_chapter_info(chat_id)
                tasks["game_state"] = self._get_game_state(chat_id)

            # 执行所有查询
            results = await asyncio.gather(*[tasks[key] for key in tasks.keys()])
            # 将结果重新映射回字典
            results_map = dict(zip(tasks.keys(), results))

            # 5. 组装最终提示词
            prompt = await self._assemble_final_prompt_v3(
                agent_info=agent_info,
                results_map=results_map,
                user_message=user_message,
                mode=mode,
                chat_id=chat_id
            )
            
            print(f"SUCCESS: 为会话 {chat_id} 构建了{mode}模式的提示词")
            return prompt

        except Exception as e:
            print(f"ERROR: 构建提示词失败: {e}")
            # 降级方案：返回基础提示词
            return await self._build_fallback_prompt(agent_id, user_message)
    

    
    async def _assemble_final_prompt(
        self,
        agent_info: Dict[str, Any],
        relevant_memories: List[Dict[str, Any]],
        chat_summary: Optional[str],
        recent_messages: List[Dict[str, Any]],
        user_message: str
    ) -> str:
        """组装最终的提示词"""
        
        prompt_parts = []
        
        # 1. 角色核心设定
        prompt_parts.append("# 角色设定")
        prompt_parts.append(f"你是 {agent_info['name']}。")
        
        if agent_info.get('personality'):
            prompt_parts.append(f"性格特征：{agent_info['personality']}")
        
        if agent_info.get('scenario'):
            prompt_parts.append(f"场景设定：{agent_info['scenario']}")
        
        if agent_info.get('system_prompt'):
            prompt_parts.append(f"系统指令：{agent_info['system_prompt']}")
        elif agent_info.get('roleplay_prompt'):
            prompt_parts.append(f"角色扮演指令：{agent_info['roleplay_prompt']}")
        
        # 2. 对话摘要（宏观背景）
        if chat_summary:
            prompt_parts.append("\n# 对话背景")
            prompt_parts.append(f"之前的对话摘要：{chat_summary}")
        
        # 3. 相关记忆片段（RAG检索结果）
        if relevant_memories:
            prompt_parts.append("\n# 相关记忆")
            prompt_parts.append("以下是与当前话题相关的对话片段：")
            for i, memory in enumerate(relevant_memories, 1):
                similarity = memory.get('similarity', 0)
                content = memory.get('content', '')
                prompt_parts.append(f"{i}. (相似度: {similarity:.2f}) {content}")
        
        # 4. 近期对话历史
        if recent_messages:
            prompt_parts.append("\n# 近期对话")
            for msg in recent_messages[-5:]:  # 只显示最近5条
                role = "用户" if msg['role'] == 'user' else agent_info['name']
                prompt_parts.append(f"{role}: {msg['content']}")
        
        # 5. 当前用户输入
        prompt_parts.append(f"\n用户: {user_message}")
        
        # 6. 回复指令
        prompt_parts.append(f"\n请以 {agent_info['name']} 的身份回复。回复要自然、生动，符合角色设定。")
        
        return "\n".join(prompt_parts)
    
    async def _build_fallback_prompt(self, agent_id: str, user_message: str) -> str:
        """降级方案：构建基础提示词"""
        try:
            agent_info = await supabase_service.get_agent_by_id(agent_id)
            if not agent_info:
                return f"用户: {user_message}\n\n请回复用户的消息。"
            
            prompt = f"你是 {agent_info['name']}。\n"
            if agent_info.get('roleplay_prompt'):
                prompt += f"{agent_info['roleplay_prompt']}\n"
            prompt += f"\n用户: {user_message}\n\n请回复："
            
            return prompt
        except Exception as e:
            print(f"ERROR: 构建降级提示词失败: {e}")
            return f"用户: {user_message}\n\n请回复用户的消息。"

    async def _get_agent_with_mode_config(self, agent_id: str, mode: str) -> Optional[Dict[str, Any]]:
        """获取模式感知的角色配置"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.rpc('get_agent_with_mode_config', {
                    'p_agent_id': agent_id,
                    'p_mode': mode
                }).execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"WARN: 获取模式感知角色配置失败，回退到基础配置: {e}")
            # 回退到基础的get_agent_by_id
            return await supabase_service.get_agent_by_id(agent_id)

    async def _get_world_info_entries(self, agent_id: str, user_message: str) -> List[Dict[str, Any]]:
        """获取世界书条目"""
        try:
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.rpc('get_active_world_info_entries', {
                    'p_agent_id': agent_id,
                    'p_search_text': user_message
                }).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"WARN: 获取世界书条目失败: {e}")
            return []

    async def _get_story_chapter_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取故事章节信息"""
        try:
            # 获取聊天详情以获取story_id
            chat_details = await supabase_service.get_chat_by_id(chat_id)
            if not chat_details or not chat_details.get('story_id'):
                return None

            # 获取故事章节
            response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("story_chapters")
                .select("*")
                .eq("story_id", chat_details['story_id'])
                .order("created_at", desc=False)
                .limit(1)
                .execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"WARN: 获取故事章节信息失败: {e}")
            return None

    async def _get_game_state(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取游戏状态"""
        try:
            chat_details = await supabase_service.get_chat_by_id(chat_id)
            if not chat_details:
                return None
            return chat_details.get('game_state', {})
        except Exception as e:
            print(f"WARN: 获取游戏状态失败: {e}")
            return {}

    def _format_relationship_status(self, game_state: Optional[Dict], agent_name: str, fallback_instructions: str) -> str:
        """将游戏状态转换为自然语言关系描述"""
        if not game_state:
            return fallback_instructions or "你对主角的看法是中立的。"

        descriptions = []
        for key, value in game_state.items():
            if key.startswith(f"{agent_name}."):
                variable = key.split('.')[1]
                # 在这里添加规则来将数值转换为自然语言
                if variable == "好感度":
                    if value < 30:
                        descriptions.append(f"你对主角的好感度很低({value}/100)，你感到不信任和警惕。")
                    elif value > 70:
                        descriptions.append(f"你对主角的好感度很高({value}/100)，你感到亲近和信任。")
                    else:
                        descriptions.append(f"你对主角的好感度一般({value}/100)。")
                elif variable == "误解程度":
                    if value > 70:
                        descriptions.append(f"你对主角存在深度误解({value}/100)，这影响着你的判断。")
                    elif value < 30:
                        descriptions.append(f"你对主角的误解已经基本消除({value}/100)。")
                    else:
                        descriptions.append(f"你对主角仍有一些误解({value}/100)。")
                elif variable == "信任度":
                    if value < 30:
                        descriptions.append(f"你对主角缺乏信任({value}/100)，保持着防备心理。")
                    elif value > 70:
                        descriptions.append(f"你对主角非常信任({value}/100)，愿意敞开心扉。")
                    else:
                        descriptions.append(f"你对主角的信任度一般({value}/100)。")
                # 可以为其他变量添加更多规则

        return " ".join(descriptions) if descriptions else fallback_instructions

    async def _assemble_final_prompt_v3(
        self,
        agent_info: Dict[str, Any],
        results_map: Dict[str, Any],
        user_message: str,
        mode: str,
        chat_id: str
    ) -> str:
        """V3版本的提示词组装器"""

        # 在故事模式下获取主角信息
        protagonist_info = None
        if mode == 'story':
            protagonist_info = await self._get_protagonist_info(chat_id)

        prompt_parts = {
            "roleplay_instructions": await self._format_roleplay_instructions(agent_info, mode, protagonist_info),
            "core_persona": self._format_persona(agent_info),
            "world_info": self._format_world_info(results_map.get("world_info", [])),
            "long_term_memory": "",  # 根据模式填充
            "short_term_memory": self._format_rag_memory(results_map.get("rag", []), agent_info),
            "recent_history": await self._format_history_with_protagonist(results_map.get("history", []), agent_info['name'], mode, protagonist_info)
        }

        # 根据模式使用不同的提示词模板
        if mode == 'story':
            # 故事模式使用新的高级模板
            story_chapter = results_map.get("story_chapter", {})
            game_state = results_map.get("game_state", {})

            # 从 agent_info 中获取预生成的初始关系描述作为后备
            fallback_instructions = agent_info.get('effective_instructions', '')

            # 格式化关系状态
            relationship_status = self._format_relationship_status(game_state, agent_info['name'], fallback_instructions)

            # 格式化对话历史
            chat_history = await self._format_history_with_protagonist(
                results_map.get("history", []), agent_info['name'], mode, protagonist_info
            )

            # 使用高级故事模板
            final_prompt = ADVANCED_STORY_CHAT_PROMPT.format(
                agent_name=agent_info['name'],
                agent_persona=self._format_persona(agent_info),
                world_info=self._format_world_info(results_map.get("world_info", [])),
                chapter_title=story_chapter.get('title', '未知章节'),
                mission_objective=story_chapter.get('mission_objective_text', '无'),
                relationship_status=relationship_status,
                chapter_event_summary=story_chapter.get('chapter_event_summary', '无特定事件。'),
                chat_history=chat_history,
                user_message=user_message
            )
            return final_prompt.strip()
        else:
            # 聊天模式使用原有逻辑
            summary_data = results_map.get("summary")
            summary_text = summary_data if isinstance(summary_data, str) else (summary_data.get('relationship_summary') or summary_data.get('summary_text') if summary_data else None)
            prompt_parts["long_term_memory"] = f"# 长期记忆摘要 (我们的对话概要)\n{summary_text or '这是我们对话的开始。'}"

            # 构建最终Prompt
            user_display_name = protagonist_info['name'] if protagonist_info else "User"
            final_prompt = f"""{prompt_parts["roleplay_instructions"]}

{prompt_parts["core_persona"]}

{prompt_parts["world_info"]}

{prompt_parts["long_term_memory"]}

{prompt_parts["short_term_memory"]}

# 近期对话历史
{prompt_parts["recent_history"]}
{user_display_name}: {user_message}
{agent_info['name']}: (你的回复必须简洁，严格控制在100个汉字以内。)
"""
            return final_prompt.strip()

    def _format_persona(self, agent: Dict[str, Any]) -> str:
        """格式化角色设定 - 模式感知版本"""
        parts = ["# 角色核心设定"]
        if agent.get('description'):
            parts.append(f"描述: {agent['description']}")
        if agent.get('personality'):
            parts.append(f"人格: {agent['personality']}")

        # 使用模式感知的场景设定
        effective_scenario = agent.get('effective_scenario') or agent.get('scenario')
        if effective_scenario:
            parts.append(f"场景: {effective_scenario}")

        # 根据模式决定是否包含背景故事
        if agent.get('should_use_backstory') and agent.get('backstory_text'):
            parts.append(f"背景故事: {agent['backstory_text']}")

        return "\n".join(parts)

    def _format_world_info(self, world_info_entries: List[Dict[str, Any]]) -> str:
        """格式化世界书信息"""
        if not world_info_entries:
            return ""
        entries = "\n".join([f"- {entry['content']}" for entry in world_info_entries])
        return f"# 世界背景与知识 (动态激活)\n{entries}"

    def _format_rag_memory(self, memories: List[Dict[str, Any]], agent_info: Dict[str, Any] = None) -> str:
        """格式化RAG记忆 - 模式感知版本"""
        memory_parts = []

        if memories:
            entries = "\n".join([f"- {mem['content']}" for mem in memories])
            memory_parts.append(f"# 精准记忆片段 (相关对话细节)\n{entries}")

        # 根据模式决定是否包含对话示例（弱化权重，仅供模仿语气）
        if agent_info and agent_info.get('should_use_mes_example') and agent_info.get('mes_example'):
            memory_parts.append(f"# 对话风格参考 (仅供模仿语气，不要照搬内容)\n{agent_info['mes_example']}")

        return "\n\n".join(memory_parts) if memory_parts else ""

    async def _get_protagonist_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取故事中的主角信息"""
        try:
            # 获取聊天详情以获取story_id
            chat_details = await supabase_service.get_chat_by_id(chat_id)
            if not chat_details or not chat_details.get('story_id'):
                return None

            # 获取故事详情以获取protagonist_agent_id
            story_detail = await supabase_service.get_story_by_id(chat_details['story_id'])
            if not story_detail or not story_detail.get('protagonist_agent_id'):
                return None

            # 获取主角Agent信息
            protagonist_agent = await supabase_service.get_agent_by_id(story_detail['protagonist_agent_id'])
            return protagonist_agent
        except Exception as e:
            print(f"WARN: 获取主角信息失败: {e}")
            return None

    async def _format_roleplay_instructions(self, agent_info: Dict[str, Any], mode: str, protagonist_info: Optional[Dict[str, Any]]) -> str:
        """格式化角色扮演指令"""
        if mode == 'story' and protagonist_info:
            return f"""你正在扮演 {agent_info['name']}。严格遵守你的角色核心设定。

# 你正在和女主角 {protagonist_info['name']} 对话。
# 以下对话历史中，"{protagonist_info['name']}" 代表玩家扮演的女主角。"""
        else:
            return f"你正在扮演 {agent_info['name']}。严格遵守你的角色核心设定。"

    async def _format_history_with_protagonist(self, messages: List[Dict[str, Any]], agent_name: str, mode: str, protagonist_info: Optional[Dict[str, Any]]) -> str:
        """格式化对话历史，在故事模式下将用户消息显示为主角"""
        if not messages:
            return ""

        formatted_messages = []
        for msg in messages[-5:]:  # 只显示最近5条
            if msg['role'] == 'user':
                # 在故事模式下，用户消息显示为主角的话
                if mode == 'story' and protagonist_info:
                    formatted_messages.append(f"{protagonist_info['name']}: {msg['content']}")
                else:
                    formatted_messages.append(f"用户: {msg['content']}")
            else:
                formatted_messages.append(f"{agent_name}: {msg['content']}")

        return "\n".join(formatted_messages)

    def _format_history(self, messages: List[Dict[str, Any]], agent_name: str) -> str:
        """格式化对话历史"""
        if not messages:
            return ""
        return "\n".join([f"{'User' if msg['role'] == 'user' else agent_name}: {msg['content']}" for msg in reversed(messages)])


# 全局实例
prompt_assembler = PromptAssembler()

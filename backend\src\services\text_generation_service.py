"""
文本生成服务
专门处理文本生成、对话、摘要等功能
"""

import asyncio
import json
from typing import List, Optional, Dict, Any, AsyncGenerator
from google.genai import types
from google import genai

from .base_llm_service import BaseLLMService, retry_on_api_error
from ..pydantic_models import (
    StructuredChatResponse, ResponseMessagePart, StoryContinuationResponse,
    StoryProgressScore, UserChoice, UserChoicesResponse,
    InteractiveSceneResponse
)
from ..prompt_templates import (
    STREAMING_ROLEPLAY_CHAT_PROMPT,
    STORY_PROGRESS_SCORING_PROMPT, USER_REPLY_CHOICE_GENERATOR_PROMPT,
    NOVEL_ANALYSIS_GDD_PROMPT_V2,
    WORLDVIEW_EXPANSION_PROMPT, AGENT_BACKSTORY_PROMPT,
    INTERACTIVE_SCENE_GENERATION_PROMPT, CHAPTER_SUMMARY_FOR_NPC_PROMPT
)

class TextGenerationService(BaseLLMService):
    """文本生成服务"""
    
    def __init__(self):
        super().__init__()
    
    async def get_streaming_chat_response(self, prompt: str) -> AsyncGenerator[str, None]:
        """统一的流式聊天响应函数"""
        try:
            client = await self.get_client()
            response_stream = await asyncio.to_thread(
                client.models.generate_content_stream,
                model=self.chat_model_name,
                contents=prompt,
                config=genai.types.GenerateContentConfig(
                    thinking_config=genai.types.ThinkingConfig(thinking_budget=0),
                    temperature=0.8, 
                )
            )
            for chunk in response_stream:
                if chunk.text:
                    yield chunk.text
        except Exception as e:
            print(f"ERROR: [Text Generation Service] Streaming chat failed: {e}")
            yield "抱歉，我好像遇到了一点麻烦，暂时无法回复你。"
    
    @retry_on_api_error()
    async def generate_text_response(self, prompt: str) -> str:
        """生成文本响应，用于摘要等非流式场景"""
        try:
            client = await self.get_client()
            response = await asyncio.to_thread(
                client.models.generate_content,
                model=self.chat_model_name,
                contents=prompt,
                config=types.GenerateContentConfig(temperature=0.7)
            )

            if not response.candidates:
                raise Exception("LLM未返回候选响应")

            text_response = response.text
            if not text_response:
                raise Exception("LLM返回空响应")

            print(f"SUCCESS: 成功生成文本响应，长度: {len(text_response)}")
            return text_response

        except Exception as e:
            print(f"ERROR: 生成文本响应失败: {e}")
            raise e
    
    @retry_on_api_error()
    async def get_story_progress_score(self, mission: str, clear_condition: str, 
                                     current_progress: int, history: List[Dict[str, str]], 
                                     user_message: str) -> StoryProgressScore:
        """评估故事进度得分（Instructor 重构）"""
        print("INFO: 正在评估故事进度...")

        # 格式化聊天历史（保留，可在 prompt 中使用）
        history_str = "\n".join([f"{msg['role']}: {msg['content']}" for msg in history[-5:]])

        prompt = STORY_PROGRESS_SCORING_PROMPT.format(
            mission=mission,
            clear_condition=clear_condition,
            current_progress=current_progress,
            user_input=user_message
        )

        score_response: StoryProgressScore = await self.generate_structured_response(
            prompt=prompt,
            response_model=StoryProgressScore,
            model_name=self.task_check_model_name,
        )

        return score_response
    
    @retry_on_api_error()
    async def generate_user_reply_choices(self, agent_name: str, agent_persona: str,
                                         chat_history: str, last_ai_message: str,
                                         chapter_task_objective: str = None,
                                         target_agent_id: str = None,
                                         include_target_agent_id: bool = False,
                                         participants: List[Dict] = None) -> List[UserChoice]:
        """生成用户回复选项（Instructor 重构）"""
        print("INFO: 正在生成用户回复选项...")
        
        # 构建上下文
        context_parts = [
            f"角色名称: {agent_name}",
            f"角色性格: {agent_persona}",
            f"最近对话: {chat_history}",
            f"AI最后回复: {last_ai_message}"
        ]
        
        if chapter_task_objective:
            context_parts.append(f"章节目标: {chapter_task_objective}")
        
        if participants:
            participants_info = ", ".join([p.get('name', 'Unknown') for p in participants])
            context_parts.append(f"对话参与者: {participants_info}")
        
        context = "\n".join(context_parts)
        
        prompt = USER_REPLY_CHOICE_GENERATOR_PROMPT.format(
            current_dialogue=context,
            character_info=agent_persona,
            situation=chapter_task_objective or "自由对话",
            last_ai_message=last_ai_message
        )
        
        response_obj: UserChoicesResponse = await self.generate_structured_response(
            prompt=prompt,
            response_model=UserChoicesResponse,
            model_name=self.chat_model_name,
        )

        # 若需要追加 target_agent_id，则在返回前处理
        if include_target_agent_id and target_agent_id:
            for choice in response_obj.choices:
                choice.target_agent_id = target_agent_id

        return response_obj.choices[:4]
    

    
    @retry_on_api_error()
    async def generate_game_design_document(self, story_summary: str) -> dict:
        """根据提炼后的故事摘要生成游戏设计文档(GDD)"""
        print("INFO: 调用LLM基于完整故事摘要生成GDD...")

        prompt = NOVEL_ANALYSIS_GDD_PROMPT_V2.format(story_summary=story_summary)

        response_data = await self.generate_json(prompt, self.story_model_name)

        # 使用Pydantic进行严格验证和解析
        try:
            from ..pydantic_models import GameDesignDocument
            gdd_model = GameDesignDocument.model_validate(response_data)
            print(f"SUCCESS: GDD生成并验证成功，包含 {len(gdd_model.chapters)} 个游戏章节")
            return gdd_model.model_dump() # 返回验证后的字典格式
        except Exception as e:
            print(f"ERROR: LLM返回的GDD结构验证失败: {e}")
            # 抛出异常，让重试装饰器捕获
            raise ValueError("GDD validation failed") from e
    
    @retry_on_api_error()
    async def generate_worldview_text(self, story_theme: str) -> str:
        """根据故事主题扩展并返回详细世界观设定文本"""
        print("INFO: 正在为故事主题扩展世界观...")
        
        prompt = WORLDVIEW_EXPANSION_PROMPT.format(story_theme=story_theme)
        
        worldview_text = await self.generate_text(prompt, self.story_model_name)
        
        print("SUCCESS: 世界观文本生成成功，长度:", len(worldview_text))
        return worldview_text
    
    @retry_on_api_error()
    async def generate_agent_backstory(self, character_info: str, story_setting: str) -> str:
        """生成角色背景故事"""
        print("INFO: 正在生成角色背景故事...")
        
        prompt = AGENT_BACKSTORY_PROMPT.format(
            character_info=character_info,
            story_setting=story_setting
        )
        
        backstory = await self.generate_text(prompt, self.story_model_name)
        
        print("SUCCESS: 角色背景故事生成成功，长度:", len(backstory))
        return backstory
    
    async def _process_single_chunk(self, chunk: str, chunk_index: int, total_chunks: int) -> str:
        """处理单个文本块 - 详细分析版本"""
        print(f"INFO: 📖 正在处理第 {chunk_index+1}/{total_chunks} 个文本块 ({((chunk_index+1)/total_chunks*100):.1f}%) - 长度: {len(chunk)} 字符")

        prompt = f"""
你是一位专业的小说分析师。请对以下小说片段进行深度分析，为后续的游戏化处理提供详细信息。

【小说片段】：
{chunk}

请按以下结构进行详细分析：

## 1. 情节概述
- 本片段的主要事件和情节发展
- 时间线和场景变化

## 2. 关键情节节点
- 重要的转折点或冲突点
- 推动剧情发展的关键事件
- 情感高潮或低谷时刻

## 3. 核心对话和台词
- 推动剧情的重要对话（保留原文）
- 体现角色性格的经典台词
- 情感表达的关键语句

## 4. 角色发展
- 主要角色在本片段中的行为和心理变化
- 角色关系的发展或变化
- 角色动机和目标

## 5. 情感线索
- 本片段的主要情感基调
- 角色间的情感互动
- 情感冲突或和解

## 6. 场景描写
- 重要的环境描写和氛围营造
- 具有象征意义的场景元素

请确保分析详细且具体，为游戏化改编提供充分的素材。
"""

        try:
            summary = await self.generate_text(prompt, self.story_model_name)
            print(f"SUCCESS: ✅ 第 {chunk_index+1} 个文本块处理完成，详细分析长度: {len(summary)}")
            return summary
        except Exception as e:
            print(f"ERROR: ❌ 第 {chunk_index+1} 个文本块处理失败: {e}")
            # 返回占位摘要，避免整个流程中断
            return f"[文本块 {chunk_index+1} 处理失败: {str(e)[:100]}...]"

    @retry_on_api_error()
    async def summarize_long_novel_in_chunks(self, novel_text: str, chunk_size: int = 30000, max_concurrent: int = 5) -> str:
        """分块摘要长篇小说 - 并发处理版本"""
        print(f"INFO: [LLM Service] 开始对长篇小说进行分块摘要提炼...")
        print(f"INFO: 📊 文本总长度: {len(novel_text)} 字符，块大小: {chunk_size} 字符，最大并发: {max_concurrent}")

        # 将小说分块
        chunks = [novel_text[i:i+chunk_size] for i in range(0, len(novel_text), chunk_size)]
        print(f"INFO: 📚 已分割为 {len(chunks)} 个文本块")

        # 使用信号量控制并发数量
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_chunk_with_semaphore(chunk: str, index: int) -> str:
            async with semaphore:
                return await self._process_single_chunk(chunk, index, len(chunks))

        # 并发处理所有块
        print(f"INFO: 🚀 开始并发处理 {len(chunks)} 个文本块...")
        tasks = [process_chunk_with_semaphore(chunk, i) for i, chunk in enumerate(chunks)]

        # 等待所有任务完成
        chunk_summaries = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_summaries = []
        success_count = 0
        for i, result in enumerate(chunk_summaries):
            if isinstance(result, Exception):
                print(f"WARN: ⚠️  第 {i+1} 个文本块处理异常: {result}")
                processed_summaries.append(f"[文本块 {i+1} 处理异常: {str(result)[:100]}...]")
            else:
                processed_summaries.append(result)
                success_count += 1

        # 合并所有摘要并进行最终整合
        combined_summary = "\n\n" + "="*50 + "\n\n".join([
            f"【第{i+1}部分分析】\n{summary}"
            for i, summary in enumerate(processed_summaries)
        ])

        # 生成整体摘要
        print(f"INFO: 🔄 正在生成整体故事摘要...")
        final_summary_prompt = f"""
基于以下分块分析，请生成一个完整的小说摘要，为游戏化改编提供全面的信息：

{combined_summary}

请按以下结构生成最终摘要：

# 小说整体分析报告

## 1. 故事概览
- 故事主题和核心冲突
- 主要故事线和副线
- 整体叙事结构

## 2. 主要角色档案
- 每个主要角色的完整弧光
- 角色关系网络
- 角色成长轨迹

## 3. 关键情节时间线
- 按时间顺序排列的重要事件
- 情节转折点和高潮时刻
- 各章节的核心冲突

## 4. 经典对话集锦
- 推动剧情的关键对话
- 体现角色性格的经典台词
- 情感表达的重要语句

## 5. 情感发展脉络
- 主要情感线的发展轨迹
- 情感冲突和解决
- 情感高潮和转折点

## 6. 游戏化要素建议
- 适合互动的关键场景
- 可以设计选择的决策点
- 角色互动的重要时刻

请确保摘要详细且结构化，为后续的游戏设计提供充分的素材。
"""

        try:
            final_summary = await self.generate_text(final_summary_prompt, self.story_model_name)
            print(f"SUCCESS: [LLM Service] 小说分块摘要完成！")
            print(f"   📊 成功处理: {success_count}/{len(chunks)} 个文本块")
            print(f"   📝 分块分析总长度: {len(combined_summary)} 字符")
            print(f"   📋 最终摘要长度: {len(final_summary)} 字符")
            return final_summary
        except Exception as e:
            print(f"WARN: ⚠️  最终摘要生成失败，返回合并的分块摘要: {e}")
            print(f"SUCCESS: [LLM Service] 小说分块摘要完成！")
            print(f"   📊 成功处理: {success_count}/{len(chunks)} 个文本块")
            print(f"   📝 合并摘要长度: {len(combined_summary)} 字符")
            return combined_summary

    @retry_on_api_error()
    async def distill_story_summary(self, novel_text: str, chunk_size: int = 30000, max_concurrent: int = 5) -> str:
        """提炼故事摘要"""
        print("INFO: [LLM Service] 正在提炼故事摘要...")

        # 如果小说文本太长，使用分块摘要
        if len(novel_text) > chunk_size:
            print(f"INFO: 📚 文本长度 {len(novel_text)} 超过阈值 {chunk_size}，使用并发分块处理")
            return await self.summarize_long_novel_in_chunks(novel_text, chunk_size, max_concurrent)

        # 对于较短的文本，直接生成详细摘要
        print(f"INFO: 📄 文本长度 {len(novel_text)} 较短，直接进行详细分析")
        prompt = f"""
你是一位专业的小说分析师。请对以下小说进行深度分析，为后续的游戏化处理提供详细信息。

【小说全文】：
{novel_text}

请按以下结构进行全面分析：

# 小说整体分析报告

## 1. 故事概览
- 故事主题和核心冲突
- 主要故事线和副线
- 整体叙事结构和时间线

## 2. 主要角色档案
- 每个主要角色的完整弧光和性格特征
- 角色关系网络和互动模式
- 角色成长轨迹和心理变化

## 3. 关键情节时间线
- 按时间顺序排列的重要事件
- 情节转折点和高潮时刻
- 各章节的核心冲突和解决

## 4. 经典对话集锦
- 推动剧情的关键对话（保留原文）
- 体现角色性格的经典台词
- 情感表达的重要语句

## 5. 情感发展脉络
- 主要情感线的发展轨迹
- 情感冲突和解决过程
- 情感高潮和转折点

## 6. 重要场景描写
- 关键场景的环境描写
- 具有象征意义的场景元素
- 氛围营造的重要片段

## 7. 游戏化要素建议
- 适合互动的关键场景
- 可以设计选择的决策点
- 角色互动的重要时刻
- 情感分支的可能性

请确保分析详细且结构化，为后续的游戏设计提供充分的素材。
"""

        summary = await self.generate_text(prompt, self.story_model_name)

        print(f"SUCCESS: [LLM Service] 故事摘要提炼完成，长度: {len(summary)}")
        return summary

    @retry_on_api_error()
    async def generate_initial_relationship_prompt(self, character_name: str, key_variables: List[str]) -> str:
        """生成初始关系描述"""
        print(f"INFO: 正在为角色 '{character_name}' 生成初始关系描述...")

        variables_str = ", ".join(key_variables) if key_variables else "无特定变量"

        prompt = f"""
为角色 '{character_name}' 生成初始关系描述，基于以下关键变量：
{variables_str}

请生成一个详细的关系描述，包括：
1. 与主角的初始关系状态
2. 情感倾向和态度
3. 互动方式和行为模式
4. 关系发展的可能方向

关系描述：
"""

        relationship_desc = await self.generate_text(prompt, self.story_model_name)

        print(f"SUCCESS: 角色 '{character_name}' 初始关系描述生成完成")
        return relationship_desc

    @retry_on_api_error()
    async def generate_interactive_scene(self, gdd: dict, chapter_gdd: dict,
                                       novel_chunk: str, agent_map: dict,
                                       character_profiles: list = None,
                                       current_game_state: dict = None,
                                       previous_chapter_summary: str = None) -> dict:
        """【已优化】一次性生成互动场景序列和章节总结"""
        print("INFO: 正在一次性生成互动场景序列和章节总结...")

        # 提取章节摘要和关键互动
        chapter_summary = chapter_gdd.get('summary', '本章摘要')
        key_interactions = chapter_gdd.get('key_interactions', [])

        # 格式化游戏状态
        game_state_str = json.dumps(current_game_state or {}, ensure_ascii=False, indent=2)

        # 格式化角色映射
        agent_id_map_str = json.dumps(agent_map, ensure_ascii=False, indent=2)

        # 格式化关键互动
        key_interactions_str = "\n".join([f"- {interaction}" for interaction in key_interactions])

        # 使用新的简化模板（现在使用agent_index_map而不是agent_id_map）
        prompt = INTERACTIVE_SCENE_GENERATION_PROMPT.format(
            current_game_state=game_state_str,
            chapter_summary=chapter_summary,
            key_interactions=key_interactions_str,
            agent_index_map=agent_id_map_str,  # 注意：这里现在传递的是索引映射
            novel_chunk=novel_chunk[:2000], # 截断以避免过长的上下文
            previous_chapter_summary=previous_chapter_summary or "无"
        )

        # 借助 instructor 生成并验证结构化数据
        scene_data: InteractiveSceneResponse = await self.generate_structured_response(
            prompt,
            response_model=InteractiveSceneResponse,
            model_name=self.story_model_name,
        )

        print("SUCCESS: 互动场景及总结已通过 Instructor 生成并验证。")
        return scene_data.model_dump()

    @retry_on_api_error()
    async def generate_chapter_summary_for_npc(self, chapter_summary: str,
                                             key_interactions: List[str]) -> str:
        """为NPC生成章节剧本摘要 - 使用优化后的模板"""
        print("INFO: 正在生成AI剧本摘要...")

        interactions_str = "\n".join([f"- {interaction}" for interaction in key_interactions])

        # 使用新的优化模板
        prompt = CHAPTER_SUMMARY_FOR_NPC_PROMPT.format(
            chapter_summary=chapter_summary,
            key_interactions=interactions_str
        )

        script_summary = await self.generate_text(prompt, self.story_model_name)

        print("SUCCESS: AI剧本摘要生成完成")
        return script_summary

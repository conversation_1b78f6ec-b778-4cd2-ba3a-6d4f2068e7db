#!/usr/bin/env python3
"""
星恋 AI 后端服务 - V5.0 统一消息流重构版
"""
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import asyncio
import uuid
import json
import base64
import traceback
from contextlib import asynccontextmanager
from typing import List, Dict, Any, Optional

import os
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Query, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from PIL import Image
import io

from src.services.unified_llm_service import UnifiedLLMService
from src.supabase_service import supabase_service
from src.imagekit_simple import simple_imagekit_service
from src.prompt_templates import STREAMING_ROLEPLAY_CHAT_PROMPT
from src.pydantic_models import CharacterCardImportRequest, ImportedAgentResponse, CharacterCardV2, TavernAICharacterCard
from src.prompt_assembler import prompt_assembler
from src.summarization_service import summarization_service

# 全局服务实例
llm_service = UnifiedLLMService()

# 全局并发锁字典，防止同一会话的竞态条件
chat_locks: Dict[str, asyncio.Lock] = {}

def get_chat_lock(chat_id: str) -> asyncio.Lock:
    """获取或创建指定chat_id的锁"""
    if chat_id not in chat_locks:
        chat_locks[chat_id] = asyncio.Lock()
    return chat_locks[chat_id]

async def regenerate_user_choices(chat_id: str):
    """
    重新生成用户回复选项
    """
    try:
        print(f"INFO: [WebSocket] 开始重新生成用户选项 for chat {chat_id}")

        # 获取聊天会话信息
        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat:
            print(f"ERROR: Chat {chat_id} not found")
            error_message = {
                "type": "choices_regenerated",
                "choices": [],
                "error": "聊天会话不存在"
            }
            await manager.broadcast(chat_id, json.dumps(error_message))
            return

        # 获取参与者信息
        participants = await supabase_service.get_chat_participants(chat_id)
        if not participants:
            print(f"ERROR: No participants found for chat {chat_id}")
            error_message = {
                "type": "choices_regenerated",
                "choices": [],
                "error": "未找到聊天参与者"
            }
            await manager.broadcast(chat_id, json.dumps(error_message))
            return

        story_id = chat.get('story_id')
        protagonist_agent_id = None
        if story_id:
            story_detail = await supabase_service.get_story_by_id(story_id)
            protagonist_agent_id = story_detail.get('protagonist_agent_id') if story_detail else None

        agent = None
        for participant in participants:
            if participant.get('id') != protagonist_agent_id:
                agent = participant
                break

        if not agent and participants:
            agent = participants[0]

        if not agent:
            print(f"ERROR: No agent found for chat {chat_id}")
            return

        # 获取最近的对话历史
        history_for_choices = await supabase_service.get_messages_by_chat_id(chat_id, limit=5)
        chat_history_str = "\n".join([f"{'用户' if msg['role'] == 'user' else agent.get('name', 'AI')}: {msg['content']}" for msg in history_for_choices])
        print(f"DEBUG: Chat history for regeneration: {chat_history_str[:200]}...")

        # 获取最后一条AI消息
        last_ai_message = ""
        for msg in reversed(history_for_choices):
            if msg['role'] == 'assistant':
                last_ai_message = msg['content']
                break

        if not last_ai_message:
            print(f"WARN: No AI message found for regenerating choices in chat {chat_id}")
            return

        print(f"DEBUG: Last AI message for regeneration: {last_ai_message[:100]}...")

        # 获取章节任务目标（如果是故事模式）
        chapter_task_objective = None
        if story_id:
            try:
                chapters = await supabase_service.get_story_chapters(story_id)
                if chapters:
                    current_chapter = chapters[0]
                    chapter_task_objective = current_chapter.get('mission_objective_text', '')
            except Exception as e:
                print(f"WARN: Failed to get chapter task objective for regeneration: {e}")

        is_story_mode = story_id is not None

        participants_list = []
        if is_story_mode:
            participants_list = await supabase_service.get_story_participants(story_id)

        user_choices = await llm_service.generate_user_reply_choices(
            agent_name=agent.get('name', 'AI'),
            agent_persona=agent.get('personality', ''),
            chat_history=chat_history_str,
            last_ai_message=last_ai_message,
            chapter_task_objective=chapter_task_objective,
            target_agent_id=agent.get('id'),
            include_target_agent_id=is_story_mode,
            participants=participants_list
        )

        choices_message = {
            "type": "choices_regenerated",
            "choices": [choice.model_dump() for choice in user_choices] if user_choices else []
        }
        await manager.broadcast(chat_id, json.dumps(choices_message, default=str))

        print(f"SUCCESS: [WebSocket] 成功重新生成 {len(user_choices)} 个用户选项 for chat {chat_id}")

    except Exception as e:
        print(f"ERROR: [WebSocket] 重新生成用户选项失败 for chat {chat_id}: {e}")
        traceback.print_exc()
        error_message = {
            "type": "choices_regenerated",
            "choices": [],
            "error": f"生成选项失败: {str(e)}"
        }
        try:
            await manager.broadcast(chat_id, json.dumps(error_message))
        except Exception as broadcast_error:
            print(f"ERROR: 发送错误消息失败: {broadcast_error}")


# --- Lifespan Manager ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("INFO: 启动 星恋AI FastAPI服务器 (V5.0 统一消息流)")
    print("=" * 50)
    health = await supabase_service.health_check()
    if health["status"] != "healthy":
        print(f"FATAL: Supabase数据库连接失败: {health.get('error')}")
    else:
        print("INFO: Supabase数据库连接成功。")
    print("INFO: 所有服务启动完成")
    yield
    print("INFO: FastAPI服务器已关闭")

# --- App 初始化 ---
app = FastAPI(title="Xingye Backend - Unified Message Stream", version="5.0.0", lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===================================================================
# API 端点 (V5 重构)
# ===================================================================

@app.get("/")
async def root():
    return {"message": "星恋 AI 后端服务运行中 - V5.0 统一消息流"}

@app.get("/health")
async def health_check():
    health = await supabase_service.health_check()
    return {
        "status": "healthy" if health["status"] == "healthy" else "unhealthy",
        "details": health
    }

# --- 新增：游客登录接口 (V4 - 缓存优先版) ---
class GuestLoginRequest(BaseModel):
    guest_id: str

@app.post("/api/auth/guest-login", response_model=Dict[str, Any])
async def guest_login(request: GuestLoginRequest):
    try:
        guest_email = f"guest_{request.guest_id}@xinglian.app"
        guest_password = str(uuid.uuid4())

        users_response = await asyncio.to_thread(
            lambda: supabase_service.supabase.auth.admin.list_users()
        )
        users_list = getattr(users_response, 'users', [])
        existing_user = next((u for u in users_list if u.email == guest_email), None)

        if existing_user:
            print(f"INFO: 游客 '{guest_email}' 已存在。正在为其更新密码以确保可登录。")
            await asyncio.to_thread(
                lambda: supabase_service.supabase.auth.admin.update_user_by_id(
                    existing_user.id,
                    attributes={'password': guest_password}
                )
            )
        else:
            print(f"INFO: 游客 '{guest_email}' 不存在，正在为其创建新账户...")
            await asyncio.to_thread(
                lambda: supabase_service.supabase.auth.admin.create_user({
                    "email": guest_email,
                    "password": guest_password,
                    "email_confirm": True,
                    "user_metadata": { "display_name": f"游客_{request.guest_id[-6:]}" }
                })
            )
            await asyncio.sleep(1.5)
            print(f"INFO: 游客 '{guest_email}' 账户创建成功。")

        print(f"INFO: 后端正在为 '{guest_email}' 登录以获取会话...")
        session_response = await asyncio.to_thread(
            lambda: supabase_service.supabase.auth.sign_in_with_password({
                "email": guest_email,
                "password": guest_password
            })
        )
        
        print(f"INFO: 成功获取会话，返回给前端。")
        return session_response.model_dump()

    except Exception as e:
        print(f"ERROR: 游客登录/创建流程失败: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"游客登录失败: {str(e)}")

# --- 对话创建 (入口) API ---

@app.post("/api/chats/start-with-agent/{agent_id}", response_model=Dict[str, str])
async def start_chat_with_agent(agent_id: str, user_id: str = Query(..., description="发起聊天的用户ID")):
    try:
        print(f"INFO: 开始处理用户 {user_id} 与角色 {agent_id} 的会话请求")

        existing_chat = await supabase_service.get_chat_by_user_and_agent(user_id, agent_id)
        if existing_chat:
            print(f"SUCCESS: 找到现有会话 {existing_chat['id']}，用户 {user_id} 与角色 {agent_id}")
            return {"chat_id": existing_chat["id"]}

        print(f"INFO: 未找到现有会话，为用户 {user_id} 与角色 {agent_id} 创建新会话")

        agent = await supabase_service.get_agent_by_id(agent_id)
        if not agent:
            print(f"ERROR: 角色 {agent_id} 不存在")
            raise HTTPException(status_code=404, detail="Agent not found")

        chat_id = await supabase_service.create_chat_session(user_id=user_id, agent_ids=[agent_id])
        if not chat_id:
            print(f"ERROR: 为用户 {user_id} 与角色 {agent_id} 创建会话失败")
            raise HTTPException(status_code=500, detail="Failed to create chat session")

        print(f"SUCCESS: 成功创建新会话 {chat_id}，用户 {user_id} 与角色 {agent_id}")

        if agent.get("first_mes"):
            await supabase_service.add_message_to_chat(
                chat_id=chat_id,
                role='assistant',
                content=agent["first_mes"],
                agent_id=agent_id
            )
            print(f"INFO: 已添加角色 {agent_id} 的开场白到会话 {chat_id}")

        return {"chat_id": chat_id}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@app.post("/api/chats/start-story/{story_id}", response_model=Dict[str, str])
async def start_story_chat(story_id: str, user_id: str = Query(..., description="发起故事的用户ID")):
    try:
        existing_chat = await supabase_service.get_chat_by_user_and_story(user_id, story_id)
        if existing_chat:
            print(f"INFO: Found existing chat {existing_chat['id']} for user {user_id} and story {story_id}.")
            return {"chat_id": existing_chat["id"]}

        story = await supabase_service.get_story_by_id(story_id)
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")
        
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise HTTPException(status_code=404, detail="Story has no chapters")
        first_chapter = chapters[0]

        opening_sequence = first_chapter.get("opening_sequence") or []
        
        agent_ids = list(set(
            element.get("agent_id") or element.get("character_id")
            for element in opening_sequence
            if element.get("agent_id") or element.get("character_id")
        ))

        print(f"--- AGENT_IDS DEBUG ---")
        print(f"Opening sequence length: {len(opening_sequence)}")
        print(f"Extracted agent_ids: {agent_ids}")
        for i, element in enumerate(opening_sequence[:3]):
            agent_id = element.get('agent_id') or element.get('character_id')
            print(f"  Element {i}: type={element.get('element_type')}, agent_id={agent_id}")
        print(f"----------------------")

        if not agent_ids:
            print(f"WARN: Story {story_id} chapter 1 has no characters in opening sequence.")
            agents = await supabase_service.get_agents(is_public=True, limit=2)
            agent_ids = [a['id'] for a in agents]

        chat_id = await supabase_service.create_chat_session(user_id=user_id, agent_ids=agent_ids, story_id=story_id)
        if not chat_id:
            raise HTTPException(status_code=500, detail="Failed to create chat session for story")

        return {"chat_id": chat_id}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@app.post("/api/chats/{chat_id}/touch")
async def touch_chat_session(chat_id: str):
    updated = await supabase_service.touch_chat(chat_id)
    if updated:
        return {"status": "ok"}
    raise HTTPException(status_code=404, detail="Chat not found or failed to update")

# --- 数据获取 API ---

@app.get("/api/chats/{chat_id}/messages", response_model=List[Dict])
async def get_chat_messages(chat_id: str, limit: int = 50, offset: int = 0):
    messages = await supabase_service.get_messages_by_chat_id(chat_id, limit, offset)
    return messages

@app.get("/api/chats/{chat_id}/details", response_model=Dict)
async def get_chat_details(chat_id: str):
    chat_details = await supabase_service.get_chat_by_id(chat_id)
    if not chat_details:
        raise HTTPException(status_code=404, detail="Chat session not found")
    return chat_details

@app.get("/api/chats/{chat_id}/participants", response_model=List[Dict])
async def get_chat_participants(chat_id: str):
    participants = await supabase_service.get_chat_participants(chat_id)
    return participants

@app.get("/api/user-chats", response_model=List[Dict])
async def get_user_chat_list(user_id: str, limit: int = 20):
    chat_list = await supabase_service.get_user_chat_list(user_id, limit)
    return chat_list

# --- 新增: 排行榜 & 发现 API ---

@app.get("/api/rankings", response_model=List[Dict])
async def get_rankings(type: str = Query(..., pattern="^(story|agent)$"), period: str = Query("daily")):
    try:
        if type == "story":
            rankings = await supabase_service.get_story_rankings(period)
        else:
            rankings = await supabase_service.get_agent_rankings(period)
        return rankings
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch rankings: {e}")

@app.get("/api/agents/public-with-creator", response_model=List[Dict])
async def get_public_agents_with_creator(limit: int = Query(10, ge=1, le=50)):
    try:
        agents = await supabase_service.get_public_agents_with_creator(limit)
        return agents
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch agents: {e}")

@app.get("/api/agents/{agent_id}", response_model=Dict)
async def get_agent_detail(agent_id: str):
    try:
        agent = await supabase_service.get_agent_by_id(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        try:
            from .pydantic_models import AgentDetailResponse
            validated_agent = AgentDetailResponse.model_validate(agent)
            return validated_agent.model_dump()
        except Exception as validation_error:
            print(f"WARN: Agent详情数据验证失败，返回原始数据: {validation_error}")
            return agent
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch agent detail: {e}")

# --- 角色卡导入 API ---

@app.post("/api/agents/import", response_model=ImportedAgentResponse)
async def import_character_card(file: UploadFile = File(...), user_id: str = Form(...)):
    try:
        if file.content_type == 'image/png':
            file_content = await file.read()
            image = Image.open(io.BytesIO(file_content))
            chara_data_b64 = image.info.get('chara')
            if not chara_data_b64:
                raise HTTPException(status_code=400, detail="PNG文件中未找到角色卡数据")
            try:
                json_data = json.loads(base64.b64decode(chara_data_b64))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"角色卡数据解析失败: {str(e)}")
        elif file.content_type == 'application/json':
            file_content = await file.read()
            try:
                json_data = json.loads(file_content.decode('utf-8'))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"JSON文件解析失败: {str(e)}")
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式，请上传PNG或JSON文件")

        if 'spec' in json_data and 'data' in json_data:
            spec = json_data.get('spec', 'chara_card_v2')
            spec_version = json_data.get('spec_version', '2.0')
            character_data = json_data['data']
        else:
            spec = 'chara_card_v1'
            spec_version = '1.0'
            character_data = json_data

        if not character_data.get('name'):
            raise HTTPException(status_code=400, detail="角色卡缺少必要的name字段")

        new_agent = await supabase_service.create_agent_from_character_card(
            user_id=user_id,
            character_data=character_data,
            spec=spec,
            spec_version=spec_version
        )

        if not new_agent:
            raise HTTPException(status_code=500, detail="角色卡导入失败")

        return ImportedAgentResponse(
            id=str(new_agent['id']),
            name=new_agent['name'],
            message=f"角色 '{new_agent['name']}' 导入成功！"
        )
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"导入角色卡时发生错误: {str(e)}")

@app.get("/api/stories", response_model=List[Dict])
async def get_stories(is_public: bool = Query(True), limit: int = Query(10, ge=1, le=50), user_id: Optional[str] = None):
    try:
        stories = await supabase_service.get_stories(user_id=user_id, is_public=is_public, limit=limit)
        return stories
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch stories: {e}")

@app.get("/api/stories/{story_id}", response_model=Dict)
async def get_story_detail(story_id: str):
    try:
        story = await supabase_service.get_story_by_id(story_id)
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")

        chapters = await supabase_service.get_story_chapters(story_id)

        try:
            agents_response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("story_agents")
                .select("agents(*)")
                .eq("story_id", story_id)
                .execute()
            )
            agents = [item['agents'] for item in agents_response.data] if agents_response.data else []
            for agent in agents:
                if 'first_mes' in agent and 'opening_line' not in agent:
                    agent['opening_line'] = agent['first_mes']
            print(f"DEBUG: Found {len(agents)} agents for story {story_id}")
        except Exception as agents_e:
            print(f"WARN: Failed to fetch story agents, falling back to public agents: {agents_e}")
            agents = await supabase_service.get_agents(is_public=True, limit=20)

        return {
            **story,
            "chapters": chapters,
            "agents": agents,
        }
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch story detail: {e}")

# --- 公开配置 API ---

class PublicConfig(BaseModel):
    supabase_url: str
    supabase_anon_key: str

@app.get("/api/config/public", response_model=PublicConfig)
async def get_public_config():
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_anon_key = os.getenv("SUPABASE_ANON_KEY")

    if not supabase_url or not supabase_anon_key:
        raise HTTPException(status_code=500, detail="Server configuration is incomplete.")

    return {
        "supabase_url": supabase_url,
        "supabase_anon_key": supabase_anon_key,
    }

# --- 独立评分API (PRD核心要求) ---

class EvaluateRequest(BaseModel):
    user_message: str

@app.post("/api/chats/{chat_id}/evaluate", response_model=Dict[str, Any])
async def evaluate_user_message_for_story(chat_id: str, request: EvaluateRequest):
    try:
        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat or not chat.get('story_id'):
            raise HTTPException(status_code=404, detail="Story chat session not found.")

        story_id = chat['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise HTTPException(status_code=404, detail="Story has no chapters.")
        
        task_progress = chat.get('task_progress', {})

        current_chapter_id = task_progress.get('current_chapter_id')
        if not current_chapter_id and chapters:
            current_chapter_id = chapters[0]['id']

        current_progress = task_progress.get('chapters', {}).get(current_chapter_id, {}).get('progress', 0)

        if current_progress >= 100:
            return {
                "progress_increment": 0,
                "reasoning": "本章任务已完成。",
                "current_progress": 100,
                "chapter_complete": True
            }

        current_chapter = next((ch for ch in chapters if ch['id'] == current_chapter_id), chapters[0] if chapters else None)
        if not current_chapter:
            raise HTTPException(status_code=404, detail="Current chapter not found.")

        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=10)

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=request.user_message
        )

        new_progress = min(current_progress + score_response.progress_increment, 100)
        new_status = "completed" if new_progress >= 100 else "in_progress"

        updated_task_progress = task_progress.copy()
        if 'chapters' not in updated_task_progress:
            updated_task_progress['chapters'] = {}
        if 'current_chapter_id' not in updated_task_progress:
            updated_task_progress['current_chapter_id'] = current_chapter['id']

        updated_task_progress['chapters'][current_chapter['id']] = {
            'progress': new_progress,
            'status': new_status
        }

        if new_progress >= 100:
            current_chapter_index = next((i for i, ch in enumerate(chapters) if ch['id'] == current_chapter['id']), -1)
            if current_chapter_index >= 0 and current_chapter_index + 1 < len(chapters):
                next_chapter = chapters[current_chapter_index + 1]
                if next_chapter['id'] not in updated_task_progress['chapters']:
                    updated_task_progress['chapters'][next_chapter['id']] = {
                        'progress': 0,
                        'status': 'unlocked'
                    }

        await supabase_service.update_chat_progress(chat_id, updated_task_progress)

        return {
            "progress_increment": score_response.progress_increment,
            "current_progress": new_progress,
            "chapter_complete": new_progress >= 100
        }

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to evaluate message: {e}")

# --- 章节推进 API ---

@app.post("/api/chats/{chat_id}/next-chapter", response_model=Dict[str, str])
async def advance_to_next_chapter(chat_id: str, user_id: str = Query(...)):
    try:
        response = await asyncio.to_thread(
            lambda: supabase_service.supabase.rpc('advance_to_next_chapter', {
                'p_current_chat_id': chat_id,
                'p_user_id': user_id
            }).execute()
        )

        if response.error:
            raise Exception(response.error.message)

        new_chat_id = response.data

        if not new_chat_id:
            raise HTTPException(status_code=404, detail="已经是最后一章或找不到下一章。")

        return {"new_chat_id": new_chat_id}

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"进入下一章失败: {str(e)}")

# --- 摘要与记忆管理 API ---

@app.post("/api/chats/{chat_id}/summary/update")
async def update_chat_summary(chat_id: str):
    try:
        success = await summarization_service.update_chat_summary(chat_id)
        if success:
            return {"status": "success", "message": "摘要已更新"}
        else:
            raise HTTPException(status_code=500, detail="摘要更新失败")
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to update summary: {e}")

@app.get("/api/chats/{chat_id}/summary")
async def get_chat_summary(chat_id: str):
    try:
        summary = await summarization_service.get_chat_summary(chat_id)
        return {
            "chat_id": chat_id,
            "summary": summary,
            "has_summary": summary is not None
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get summary: {e}")

@app.get("/api/chats/{chat_id}/memory-status")
async def get_memory_status(chat_id: str):
    try:
        total_messages = await asyncio.to_thread(
            lambda: supabase_service.supabase.table("messages")
            .select("id", count="exact")
            .eq("chat_id", chat_id)
            .execute()
        )
        vectorized_messages = await asyncio.to_thread(
            lambda: supabase_service.supabase.table("messages")
            .select("id", count="exact")
            .eq("chat_id", chat_id)
            .not_.is_("embedding", "null")
            .execute()
        )
        summary = await summarization_service.get_chat_summary(chat_id)

        return {
            "chat_id": chat_id,
            "total_messages": total_messages.count or 0,
            "vectorized_messages": vectorized_messages.count or 0,
            "has_summary": summary is not None,
            "memory_coverage": (vectorized_messages.count or 0) / max(total_messages.count or 1, 1) * 100
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get memory status: {e}")

# ===================================================================
# WebSocket (V5 重构)
# ===================================================================

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, chat_id: str):
        await websocket.accept()
        if chat_id not in self.active_connections:
            self.active_connections[chat_id] = []
        self.active_connections[chat_id].append(websocket)
        print(f"INFO: WebSocket connected to chat {chat_id}. Total connections: {len(self.active_connections[chat_id])}")

    def disconnect(self, websocket: WebSocket, chat_id: str):
        if chat_id in self.active_connections:
            self.active_connections[chat_id].remove(websocket)
            if not self.active_connections[chat_id]:
                del chat_locks[chat_id] # Clean up lock when no connections left
                del self.active_connections[chat_id]
        print(f"INFO: WebSocket disconnected from chat {chat_id}.")

    async def broadcast(self, chat_id: str, message: str):
        if chat_id in self.active_connections:
            for connection in self.active_connections[chat_id]:
                await connection.send_text(message)

manager = ConnectionManager()

# ===================================================================
# 织梦者引擎 - 选择效果处理
# ===================================================================

async def process_choice_effect(chat_id: str, choice_effect: dict):
    try:
        print(f"INFO: 处理选择效果 for chat {chat_id}: {choice_effect}")

        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat:
            print(f"ERROR: Chat {chat_id} not found")
            return

        current_game_state = chat.get('game_state', {})
        
        if current_game_state is None:
            current_game_state = {}

        updated_game_state = dict(current_game_state)
        state_changes = []

        for variable_name, effect_value in choice_effect.items():
            if isinstance(effect_value, str):
                delta = int(effect_value)
            else:
                delta = int(effect_value)

            current_value = updated_game_state.get(variable_name, 50)
            new_value = max(0, min(100, current_value + delta))
            updated_game_state[variable_name] = new_value

            state_changes.append({
                "variable": variable_name,
                "old_value": current_value,
                "new_value": new_value,
                "delta": delta
            })
            print(f"  - {variable_name}: {current_value} -> {new_value} ({delta:+d})")

        await supabase_service.update_chat_game_state(chat_id, updated_game_state)

        change_descriptions = []
        for change in state_changes:
            variable_parts = change["variable"].split(".")
            if len(variable_parts) >= 2:
                character_name = variable_parts[0]
                variable_type = variable_parts[1]
                if change["delta"] > 0:
                    change_descriptions.append(f"{character_name}的{variable_type}提升了！")
                elif change["delta"] < 0:
                    change_descriptions.append(f"{character_name}的{variable_type}下降了...")

        if change_descriptions:
            await manager.broadcast(chat_id, json.dumps({
                "type": "game_state_update",
                "changes": state_changes,
                "descriptions": change_descriptions,
                "new_game_state": updated_game_state
            }))
            print(f"SUCCESS: 游戏状态更新完成 for chat {chat_id}")

    except Exception as e:
        print(f"ERROR: 处理选择效果失败 for chat {chat_id}: {e}")
        traceback.print_exc()

@app.websocket("/ws/chat/{chat_id}")
async def websocket_chat_endpoint(websocket: WebSocket, chat_id: str, user_id: str = Query(...)):
    await manager.connect(websocket, chat_id)
    try:
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if not chat_session:
            await websocket.send_text(json.dumps({"type": "error", "content": "Chat session not found."}))
            return

        is_story_mode = chat_session.get("story_id") is not None
        protagonist_agent_id = None

        if is_story_mode:
            story_id = chat_session.get("story_id")
            story_detail = await supabase_service.get_story_by_id(story_id)
            if story_detail:
                protagonist_agent_id = story_detail.get("protagonist_agent_id")
                print(f"=== PROTAGONIST DEBUG (Backend) ===\nStory ID: {story_id}\nStory title: {story_detail.get('title')}\nProtagonist agent ID: {protagonist_agent_id}\n=====================================")

        participants = await supabase_service.get_chat_participants(chat_id)

        if is_story_mode and protagonist_agent_id:
            protagonist_agent = await supabase_service.get_agent_by_id(protagonist_agent_id)
            if protagonist_agent:
                participants.append(protagonist_agent)
                print(f"DEBUG: Added protagonist agent {protagonist_agent.get('name')} to participants list")

        initial_state_package = {
            "type": "game_state_sync",
            "data": {
                "chat_id": chat_id,
                "is_story_mode": is_story_mode,
                "protagonist_agent_id": protagonist_agent_id,
                "messages": await supabase_service.get_messages_by_chat_id(chat_id, limit=100),
                "participants": participants,
                "task_progress": chat_session.get("task_progress", {}),
            }
        }
        await websocket.send_text(json.dumps(initial_state_package, default=str))
        print(f"INFO: Sent initial game state for chat {chat_id}")
        
        if is_story_mode:
            print(f"INFO: Chat {chat_id} is in Story Mode.")

        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)

            action = message_data.get("action")
            if is_story_mode and action == 'next':
                 asyncio.create_task(process_opening_sequence(chat_id))
                 continue

            if action == 'regenerate_choices':
                asyncio.create_task(regenerate_user_choices(chat_id))
                continue

            content = message_data.get("content", "").strip()
            if not content:
                print(f"INFO: Received empty content message, skipping...")
                continue

            user_message = await supabase_service.add_message_to_chat(
                chat_id=chat_id,
                role='user',
                content=content,
                agent_id=None
            )
            if not user_message:
                await websocket.send_text(json.dumps({"type": "error", "content": "Failed to save user message."}))
                continue

            if user_message.get('id'):
                asyncio.create_task(generate_and_save_embedding(user_message['id'], content))

            target_agent_id = message_data.get("target_agent_id")
            target_agent_index = message_data.get("target_agent_index")

            if is_story_mode:
                chat_session = await supabase_service.get_chat_by_id(chat_id)
                task_progress = chat_session.get('task_progress', {})
                opening_sequence_index = task_progress.get('opening_sequence_index', 0)

                story_id = chat_session.get('story_id')
                chapters = await supabase_service.get_story_chapters(story_id)
                opening_sequence_total = len(chapters[0].get("opening_sequence", [])) if chapters else 0

                if opening_sequence_index < opening_sequence_total:
                    print(f"INFO: 玩家在演绎序列中做出选择，继续播放下一个元素...")
                    print(f"INFO: 为演绎过程中的选择进行评分...")
                    asyncio.create_task(process_choice_scoring_only(chat_id, user_id, content, target_agent_id))
                    asyncio.create_task(process_opening_sequence(chat_id))
                else:
                    print(f"INFO: 自由对话阶段，触发AI回复...")
                    asyncio.create_task(process_story_turn_with_scoring(chat_id, user_id, content, target_agent_id, target_agent_index))
            else:
                asyncio.create_task(process_ai_turn(chat_id, user_id, target_agent_id, is_story_mode))

    except WebSocketDisconnect:
        manager.disconnect(websocket, chat_id)
    except Exception as e:
        print(f"ERROR: WebSocket for chat {chat_id} error: {e}")
        traceback.print_exc()
        manager.disconnect(websocket, chat_id)


async def process_ai_turn(chat_id: str, user_id: str, target_agent_id: Optional[str], is_story_mode: bool):
    lock = get_chat_lock(chat_id)
    async with lock:
        try:
            history = await supabase_service.get_messages_by_chat_id(chat_id, limit=20)
            participants = await supabase_service.get_chat_participants(chat_id)
            
            user_message = None
            for msg in reversed(history):
                if msg['role'] == 'user':
                    user_message = msg['content']
                    break
            
            if not user_message:
                raise ValueError("No user message found to respond to")
            
            if target_agent_id:
                target_agent = next((p for p in participants if p['id'] == target_agent_id), None)
            else:
                target_agent = participants[0] if participants else None
                
            if not target_agent:
                raise ValueError("No agent available for reply")
            
            if is_story_mode:
                chat = await supabase_service.get_chat_by_id(chat_id)
                await process_story_turn(chat, history, user_message, target_agent, participants)
            else:
                await process_regular_chat_turn(chat_id, history, user_message, target_agent)

        except Exception as e:
            print(f"ERROR: Processing AI turn for chat {chat_id} failed: {e}")
            traceback.print_exc()
            await manager.broadcast(chat_id, json.dumps({"type": "error", "content": "AI response generation failed."}))

async def process_regular_chat_turn(chat_id: str, history: list, user_message: str, agent: dict):
    try:
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if not chat_session:
            raise ValueError("会话不存在")

        is_story_mode = chat_session.get("story_id") is not None
        mode = 'story' if is_story_mode else 'chat'

        prompt = await prompt_assembler.build_fast_prompt(
            chat_id=chat_id, user_message=user_message, agent_id=agent['id'], mode=mode
        )
        print(f"SUCCESS: 为角色 {agent.get('name')} 构建了{mode}模式的快速提示词")
    except Exception as e:
        print(f"WARN: 快速提示词构建失败，使用降级方案: {e}")
        prompt = STREAMING_ROLEPLAY_CHAT_PROMPT.format(
            agent_name=agent.get('name', 'AI'),
            agent_persona=agent.get('roleplay_prompt', ''),
            chat_history="\n".join([f"{msg['role']}: {msg['content']}" for msg in history[-5:]]),
            user_message=user_message,
        )

    await stream_and_save_response(chat_id, agent, prompt, is_story_mode)
    asyncio.create_task(process_rag_enhancement_async(chat_id, user_message, agent['id'], mode))

async def process_rag_enhancement_async(chat_id: str, user_message: str, agent_id: str, mode: str):
    try:
        await prompt_assembler.build_prompt(
            chat_id=chat_id, user_message=user_message, agent_id=agent_id, mode=mode
        )
        print(f"SUCCESS: 异步生成了完整的RAG增强提示词（用于未来优化）")
    except Exception as e:
        print(f"WARN: 异步RAG处理失败: {e}")

# 新增一个辅助函数，用于在后台生成并广播选项
async def generate_and_broadcast_choices(chat_id: str, agent: dict, full_ai_response: str, is_story_mode: bool):
    """在后台生成用户选项并广播"""
    try:
        # 生成用户回复选项
        history_for_choices = await supabase_service.get_messages_by_chat_id(chat_id, limit=5)
        chat_history_str = "\n".join([f"{'用户' if msg['role'] == 'user' else agent.get('name', 'AI')}: {msg['content']}" for msg in history_for_choices])

        chapter_task_objective = None
        participants_list = []
        if is_story_mode:
            chat_session = await supabase_service.get_chat_by_id(chat_id)
            story_id = chat_session.get('story_id')
            if story_id:
                chapters = await supabase_service.get_story_chapters(story_id)
                if chapters:
                    current_chapter = chapters[0]
                    chapter_task_objective = current_chapter.get('mission_objective_text', '')
                participants_list = await supabase_service.get_story_participants(story_id)

        user_choices = await llm_service.generate_user_reply_choices(
            agent_name=agent.get('name', 'AI'),
            agent_persona=agent.get('personality', ''),
            chat_history=chat_history_str,
            last_ai_message=full_ai_response.strip(),
            chapter_task_objective=chapter_task_objective,
            target_agent_id=agent.get('id'),
            include_target_agent_id=is_story_mode,
            participants=participants_list
        )

        # 广播新的 `choices_updated` 事件
        choices_payload = {
            "type": "choices_updated",
            "choices": [choice.model_dump() for choice in user_choices] if user_choices else []
        }
        await manager.broadcast(chat_id, json.dumps(choices_payload, default=str))
        print(f"SUCCESS: [BG Task] Choices generated and broadcasted for chat {chat_id}")

    except Exception as e:
        print(f"ERROR: [BG Task] Failed to generate choices for chat {chat_id}: {e}")
        # 向客户端发送一个错误通知
        await manager.broadcast(chat_id, json.dumps({"type": "choices_failed", "error": str(e)}))

async def stream_and_save_response(chat_id: str, agent: dict, prompt: str, is_story_mode: bool):
    response_stream = llm_service.get_streaming_chat_response(prompt)
    full_ai_response = ""
    temp_message_id = f"temp_{uuid.uuid4().hex}"

    # 1. 流式广播文本块 (逻辑不变，但现在是主要通信方式)
    async for chunk in response_stream:
        if chunk:
            full_ai_response += chunk
            # 立即广播收到的数据块
            await manager.broadcast(chat_id, json.dumps({
                "type": "message_chunk",
                "temp_id": temp_message_id,
                "role": "assistant",
                "agent_id": agent['id'],
                "content_chunk": chunk
            }))
            await asyncio.sleep(0.01)  # 短暂休眠以防止WebSocket拥塞

    # 2. 当文本流结束时，立即处理回复的持久化和最终确认
    if full_ai_response.strip():
        # 保存完整消息到数据库
        ai_message = await supabase_service.add_message_to_chat(
            chat_id=chat_id, role='assistant', content=full_ai_response.strip(), agent_id=agent['id']
        )

        # 广播 `stream_end` 事件，告诉前端文本已完结，并提供最终的、带永久ID的消息对象
        stream_end_payload = {
            "type": "stream_end",
            "temp_id": temp_message_id,
            "final_message": ai_message, # ai_message 应该是从数据库返回的完整消息对象
        }
        await manager.broadcast(chat_id, json.dumps(stream_end_payload, default=str))

        # 3. 将选项生成、Embedding和摘要更新都放入后台任务，不再阻塞主流程
        asyncio.create_task(
            generate_and_broadcast_choices(chat_id, agent, full_ai_response, is_story_mode)
        )

        if ai_message and ai_message.get('id'):
            asyncio.create_task(generate_and_save_embedding(ai_message['id'], full_ai_response))

        asyncio.create_task(check_and_update_summary(chat_id))

async def generate_and_save_embedding(message_id: int, content: str):
    try:
        print(f"INFO: 开始为消息 {message_id} 生成embedding...")
        embedding = await llm_service.get_embedding(content)
        success = await supabase_service.update_message_embedding(message_id, embedding)
        if success:
            print(f"SUCCESS: 消息 {message_id} 的embedding已保存，可用于RAG检索")
        else:
            print(f"WARN: 消息 {message_id} 的embedding保存失败")
    except Exception as e:
        print(f"ERROR: 为消息 {message_id} 生成embedding失败: {e}")

async def check_and_update_summary(chat_id: str):
    try:
        print(f"INFO: 检查会话 {chat_id} 是否需要更新摘要...")
        should_update = await summarization_service.should_update_summary(chat_id)
        if should_update:
            print(f"INFO: 开始为会话 {chat_id} 更新摘要...")
            success = await summarization_service.update_chat_summary(chat_id)
            if success:
                print(f"SUCCESS: 会话 {chat_id} 的摘要已更新")
            else:
                print(f"WARN: 会话 {chat_id} 的摘要更新失败")
        else:
            print(f"INFO: 会话 {chat_id} 暂时不需要更新摘要")
    except Exception as e:
        print(f"ERROR: 检查会话 {chat_id} 摘要更新失败: {e}")

async def process_choice_scoring_only(chat_id: str, user_id: str, user_message: str, target_agent_id: Optional[str] = None):
    try:
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if not chat_session or not chat_session.get('story_id'):
            print(f"ERROR: Chat {chat_id} is not a story chat")
            return

        story_id = chat_session['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            print(f"ERROR: No chapters found for story {story_id}")
            return

        current_chapter = chapters[0]
        task_progress = chat_session.get('task_progress', {})
        current_progress = task_progress.get('current_progress', 0)

        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=20)

        await asyncio.sleep(1.0)
        print(f"INFO: Starting choice scoring for chat {chat_id}")

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=user_message
        )

        if score_response:
            increment = score_response.progress_increment
            new_total = min(current_progress + increment, 100)
            is_chapter_complete = new_total >= 100

            updated_task_progress = {**task_progress, 'current_progress': new_total}
            await supabase_service.update_chat_progress(chat_id, updated_task_progress)

            score_message = {
                "type": "score_update",
                "progress_increment": increment,
                "current_progress": new_total,
                "chapter_complete": is_chapter_complete
            }
            await manager.broadcast(chat_id, json.dumps(score_message))
            print(f"INFO: Choice scoring completed for chat {chat_id}: +{increment} -> {new_total}")

    except Exception as e:
        print(f"ERROR: Choice scoring failed for chat {chat_id}: {e}")
        traceback.print_exc()

async def process_story_turn_with_scoring(chat_id: str, user_id: str, user_message: str, target_agent_id: Optional[str] = None, target_agent_index: Optional[int] = None):
    lock = get_chat_lock(chat_id)
    async with lock:
        try:
            chat_task = supabase_service.get_chat_by_id(chat_id)
            history_task = supabase_service.get_messages_by_chat_id(chat_id, limit=15)
            participants_task = supabase_service.get_chat_participants(chat_id)
            chat, history, participants = await asyncio.gather(chat_task, history_task, participants_task)

            if not chat or not chat.get('story_id'):
                raise ValueError("Not a valid story session.")

            # 选择回复角色的逻辑保持不变
            if target_agent_index is not None and target_agent_index < len(participants):
                replying_agent = participants[target_agent_index]
            elif target_agent_id:
                replying_agent = next((p for p in participants if p['id'] == target_agent_id), participants[0])
            else:
                replying_agent = participants[0] if participants else None

            if not replying_agent:
                raise ValueError("No agent available for reply")

            # --- ▼▼▼ 核心修改区域开始 ▼▼▼ ---
            # 1. 不再手动构建简单的prompt
            #    移除所有 STREAMING_STORY_CHAT_PROMPT(...) 的相关代码

            # 2. 直接调用 prompt_assembler 来构建完整的、带有故事上下文的 prompt
            print(f"INFO: [Story Mode] Building advanced prompt for agent {replying_agent.get('name')}...")
            streaming_prompt = await prompt_assembler.build_prompt(
                chat_id=chat_id,
                user_message=user_message,
                agent_id=replying_agent['id'],
                mode='story' # <--- 关键：指明这是故事模式
            )

            # 3. 将 AI 回复和评分任务并行执行
            ai_reply_task = asyncio.create_task(stream_and_save_response(chat_id, replying_agent, streaming_prompt, is_story_mode=True))

            # (评分任务的逻辑保持不变，但需要确保它能正确获取章节信息)
            story_id = chat['story_id']
            chapters = await supabase_service.get_story_chapters(story_id)
            if not chapters:
                raise ValueError("Story has no chapters.")

            task_progress = chat.get('task_progress', {})
            # 修正：从 task_progress 中获取当前章节和进度
            current_chapter_id = task_progress.get('current_chapter_id')
            current_chapter = None
            if current_chapter_id:
                current_chapter = next((ch for ch in chapters if ch['id'] == current_chapter_id), None)

            # 如果没有当前章节ID或找不到，则默认为第一个
            if current_chapter is None:
                current_chapter = chapters[0]
                current_chapter_id = current_chapter['id']

            current_progress = task_progress.get('chapters', {}).get(current_chapter_id, {}).get('progress', 0)

            scoring_task = asyncio.create_task(_delayed_scoring_task(
                chat_id=chat_id, user_message=user_message, current_chapter=current_chapter,
                current_progress=current_progress, task_progress=task_progress, history=history
            ))

            await ai_reply_task
            print(f"INFO: AI reply completed for chat {chat_id}, scoring task running in background")
            # --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---

        except Exception as e:
            print(f"ERROR: process_story_turn_with_scoring failed for chat {chat_id}: {e}")
            traceback.print_exc()

async def _delayed_scoring_task(chat_id: str, user_message: str, current_chapter: dict, current_progress: int, task_progress: dict, history: list):
    try:
        await asyncio.sleep(1.0)
        print(f"INFO: Starting delayed scoring task for chat {chat_id}")

        if current_progress >= 100:
            print(f"INFO: Chapter already completed for chat {chat_id}, skipping scoring")
            return

        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=user_message
        )

        new_progress = min(current_progress + score_response.progress_increment, 100)
        new_status = "completed" if new_progress >= 100 else "in_progress"

        updated_task_progress = task_progress.copy()
        if 'chapters' not in updated_task_progress:
            updated_task_progress['chapters'] = {}
        if 'current_chapter_id' not in updated_task_progress:
            updated_task_progress['current_chapter_id'] = current_chapter['id']

        updated_task_progress['chapters'][current_chapter['id']] = {'progress': new_progress, 'status': new_status}

        if new_progress >= 100:
            chapters = await supabase_service.get_story_chapters(current_chapter.get('story_id'))
            if chapters:
                current_chapter_index = next((i for i, ch in enumerate(chapters) if ch['id'] == current_chapter['id']), -1)
                if current_chapter_index >= 0 and current_chapter_index + 1 < len(chapters):
                    next_chapter = chapters[current_chapter_index + 1]
                    if next_chapter['id'] not in updated_task_progress['chapters']:
                        updated_task_progress['chapters'][next_chapter['id']] = {'progress': 0, 'status': 'unlocked'}

        await supabase_service.update_chat_progress(chat_id, updated_task_progress)

        await manager.broadcast(chat_id, json.dumps({
            "type": "score_update", "progress_increment": score_response.progress_increment,
            "current_progress": new_progress, "chapter_complete": new_progress >= 100
        }))
        print(f"INFO: Scoring completed for chat {chat_id}: +{score_response.progress_increment} -> {new_progress}")

    except Exception as e:
        print(f"ERROR: Delayed scoring task failed for chat {chat_id}: {e}")
        traceback.print_exc()

async def process_story_turn(chat: dict, history: list, user_message: str, agent: dict, participants: list):
    chat_id = chat['id']

    # 使用 prompt_assembler 来构建完整的、带有故事上下文的 prompt
    print(f"INFO: [Story Mode] Building advanced prompt for agent {agent.get('name')}...")
    streaming_prompt = await prompt_assembler.build_prompt(
        chat_id=chat_id,
        user_message=user_message,
        agent_id=agent['id'],
        mode='story' # 指明这是故事模式
    )

    await stream_and_save_response(chat_id, agent, streaming_prompt, is_story_mode=True)

async def process_opening_sequence(chat_id: str):
    lock = get_chat_lock(chat_id)
    async with lock:
        try:
            chat = await supabase_service.get_chat_by_id(chat_id)
            if not chat or not chat.get('story_id'):
                print(f"ERROR: Chat {chat_id} is not a story chat or not found")
                return

            story_id = chat['story_id']
            story_detail = await supabase_service.get_story_by_id(story_id)
            protagonist_agent_id = story_detail.get('protagonist_agent_id') if story_detail else None

            chapters = await supabase_service.get_story_chapters(story_id)
            if not chapters:
                print(f"ERROR: No chapters found for story {story_id}")
                return

            first_chapter = chapters[0]
            opening_sequence = first_chapter.get("opening_sequence") or []

            if not opening_sequence:
                print(f"INFO: No opening sequence found for story {story_id}")
                return

            task_progress = chat.get('task_progress', {})
            opening_progress = task_progress.get('opening_sequence_index', 0)

            if opening_progress >= len(opening_sequence):
                print(f"INFO: Opening sequence already completed for chat {chat_id}")
                return

            current_element = opening_sequence[opening_progress]
            element_type = current_element.get("element_type", "text")
            content = current_element.get("content_or_prompt", "")
            character_id = current_element.get("agent_id") or current_element.get("character_id")

            if content:
                role = 'narration'
                if character_id:
                    if character_id == protagonist_agent_id:
                        role = 'user'
                    else:
                        role = 'assistant'
                await supabase_service.add_message_to_chat(
                    chat_id=chat_id, role=role, content=content, agent_id=character_id,
                    metadata={"is_opening_sequence": True}
                )
                print(f"INFO: Saved opening sequence element to chat {chat_id} as '{role}' message (character_id: {character_id}, protagonist: {protagonist_agent_id}).")

            message_data = {
                "type": "opening_sequence_element",
                "data": {
                    "element_type": element_type, "content": content, "character_id": character_id,
                    "sequence_index": opening_progress, "total_elements": len(opening_sequence),
                    "is_last": opening_progress == len(opening_sequence) - 1
                }
            }
            if element_type == "choice" and "choices" in current_element:
                message_data["data"]["choices"] = current_element["choices"]

            await manager.broadcast(chat_id, json.dumps(message_data))

            new_progress = opening_progress + 1
            updated_task_progress = {**task_progress, 'opening_sequence_index': new_progress}
            await supabase_service.update_chat_progress(chat_id, updated_task_progress)

            print(f"INFO: Sent opening sequence element {opening_progress + 1}/{len(opening_sequence)} for chat {chat_id}")

            if new_progress >= len(opening_sequence):
                completion_message = {"type": "opening_sequence_complete", "data": {"chat_id": chat_id}}
                await manager.broadcast(chat_id, json.dumps(completion_message))
                print(f"INFO: Opening sequence completed for chat {chat_id}")

        except Exception as e:
            print(f"ERROR: Failed to process opening sequence for chat {chat_id}: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    import uvicorn
    print("INFO: 启动星恋 AI 后端服务 - V5.0 统一消息流")
    print("INFO: 访问 http://127.0.0.1:8000/docs 查看API文档")
    uvicorn.run("supabase_main:app", host="0.0.0.0", port=8000, reload=True)
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart'; // 需要引入这个库来使用 ScrollDirection
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';
import '../widgets/chat_input_bar.dart';
import '../widgets/message_list.dart';
import '../widgets/choice_buttons.dart';
import '../widgets/choice_loading_indicator.dart';

// 将 AgentChatView 转换为 StatefulWidget
class AgentChatView extends StatefulWidget {
  const AgentChatView({super.key});

  @override
  State<AgentChatView> createState() => _AgentChatViewState();
}

class _AgentChatViewState extends State<AgentChatView> with TickerProviderStateMixin {
  late final ScrollController _scrollController;
  late final AnimationController _headerAnimationController;
  late final Animation<Offset> _headerSlideAnimation;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _headerSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, -1.5), // 向上滑出
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOut,
    ));

    // 监听滚动
    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    if (_scrollController.position.userScrollDirection == ScrollDirection.reverse) {
      // 向上滚动时隐藏Header
      if (_headerAnimationController.status == AnimationStatus.dismissed) {
        _headerAnimationController.forward();
      }
    }
    if (_scrollController.position.userScrollDirection == ScrollDirection.forward) {
      // 向下滚动时显示Header
      if (_headerAnimationController.status == AnimationStatus.completed) {
        _headerAnimationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    _headerAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatPlayerBloc, ChatPlayerState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.black, // 统一背景色
          body: Stack(
            children: [
              // 1. 全屏清晰背景图 (移除了模糊)
              _buildAgentBackground(state.participants.isNotEmpty ? state.participants.first.imageUrl : null),

              // 2. UI层
              Column(
                children: [
                  // 占位符，因为我们的动态HUD会浮动在列表之上
                  const SizedBox.shrink(),

                  // 3. 消息列表
                  Expanded(
                    child: MessageList(
                      // 传递ScrollController
                      scrollController: _scrollController,
                      messages: state.messages,
                      participants: state.participants,
                      isStoryMode: state.storyDetail != null,
                      protagonistAgent: state.protagonistAgent,
                      // 顶部留出足够空间给HUD
                      customPadding: const EdgeInsets.only(top: 120, bottom: 8, left: 16, right: 16),
                    ),
                  ),

                  // 4. 输入交互区
                  // 需求 1: 使用 showChoicesPanel 控制可见性
                  if (state.showChoicesPanel) ...[
                    if (state.isGeneratingChoices && (state.currentChoices == null || state.currentChoices!.isEmpty))
                      const ChoiceLoadingIndicator(),
                    if (state.currentChoices != null && state.currentChoices!.isNotEmpty)
                      const ChoiceButtons(),
                  ],
                  if (state.isReplying && !state.isGeneratingChoices && !state.showChoicesPanel)
                    _buildTypingIndicator(context),
                  ChatInputBar(state: state),
                ],
              ),

              // 5. 动态浮动的HUD
              _buildDynamicHeader(state),
            ],
          ),
        );
      },
    );
  }

  // 修改背景图构建方法，移除模糊
  Widget _buildAgentBackground(String? imageUrl) {
    return Positioned.fill(
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 800),
        child: Container(
          key: ValueKey<String?>(imageUrl),
          decoration: BoxDecoration(
            image: imageUrl != null && imageUrl.isNotEmpty
                ? DecorationImage(image: NetworkImage(imageUrl), fit: BoxFit.cover, alignment: Alignment.topCenter)
                : null,
            color: AppColors.background,
          ),
          // 添加渐变遮罩以确保文本可读性
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.black.withOpacity(0.6), Colors.transparent, Colors.black.withOpacity(0.8)],
                stops: const [0.0, 0.4, 1.0],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 新增：构建动态HUD的方法
  Widget _buildDynamicHeader(ChatPlayerState state) {
    // agent 变量暂时不使用，但保留以备将来扩展
    return SlideTransition(
      position: _headerSlideAnimation,
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildGlassButton(icon: Icons.arrow_back, onTap: () => context.pop()),
                  const Spacer(),
                  // --- 变更点 1 & 2：修改图标 ---
                  _buildGlassButton(icon: Icons.star_outline, onTap: () {}), // 原为 search
                  const SizedBox(width: 8),
                  _buildGlassButton(icon: Icons.settings_outlined, onTap: () {}), // 原为 more_horiz
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  // 等级
                  _buildGlassPill(
                    child: Row(
                      children: [
                        const Icon(Icons.star, color: AppColors.accentYellow, size: 14),
                        const SizedBox(width: 4),
                        Text('Lv.2', style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 解锁信息
                  _buildGlassPill(
                    child: Row(
                      children: [
                        const Icon(Icons.lock_open, color: Colors.white70, size: 12),
                        const SizedBox(width: 4),
                        Text('Lv.3解锁TA的日记', style: const TextStyle(color: Colors.white70, fontSize: 10)),
                      ],
                    ),
                  ),
                  const Spacer(),
                  // 右侧按钮组
                  _buildGlassPill(child: const Text('羁绊值', style: TextStyle(color: Colors.white, fontSize: 12))),
                  const SizedBox(width: 8),
                  _buildGlassPill(child: const Text('日记', style: TextStyle(color: Colors.white, fontSize: 12))),
                  const SizedBox(width: 8),
                  _buildGlassPill(child: const Text('约会', style: TextStyle(color: Colors.white, fontSize: 12))),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  // 辅助方法：构建毛玻璃按钮
  Widget _buildGlassButton({required IconData icon, required VoidCallback onTap}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(18),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: InkWell(
          onTap: onTap,
          child: Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
        ),
      ),
    );
  }

  // 辅助方法：构建毛玻璃胶囊
  Widget _buildGlassPill({required Widget child}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(15),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: child,
        ),
      ),
    );
  }

  Widget _buildTypingIndicator(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          const SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(strokeWidth: 2, color: AppColors.secondaryText),
          ),
          const SizedBox(width: 8),
          Text(
            '对方正在输入...',
            style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14),
          ),
        ],
      ),
    );
  }
}